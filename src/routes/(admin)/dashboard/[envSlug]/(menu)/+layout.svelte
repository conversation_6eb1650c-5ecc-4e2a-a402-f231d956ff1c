<script lang="ts">
  import { fly } from "svelte/transition"
  import { Menu } from "lucide-svelte"

  import { buttonVariants } from "$lib/components/ui/button"
  import * as Dialog from "$lib/components/ui/dialog"
  import { page } from "$app/stores"
  import { getEnvironmentState } from "$lib/states"

  let { data, children } = $props()

  let { session } = data

  let open = $state(false)

  const basePath = "/dashboard/"

  const environment = getEnvironmentState()

  class NavItem {
    href: string
    label: string
    active: boolean

    constructor(
      href: string,
      label: string,
      isActive: (href: string) => boolean,
    ) {
      this.href = href
      this.label = label
      this.active = isActive(this.href)
    }
  }

  let navItems = $state<NavItem[]>([])

  $effect(() => {
    navItems = [
      new NavItem(
        `${basePath}${environment.value?.slug}`,
        "Home",
        (href) => $page.url.pathname === href,
      ),
    ]
  })

  let settingsItem = $state<NavItem>()

  $effect(() => {
    settingsItem = new NavItem(
      `${basePath}${environment.value?.slug}/settings`,
      "⚙️",
      (href) => $page.url.pathname.startsWith(href),
    )
  })
</script>

<div
  class="grid grid-rows-[auto_1fr] lg:grid-rows-1 lg:grid-cols-[auto_1fr] overflow-hidden top-0 bottom-0 right-0 left-0 absolute"
>
  <nav
    class="w-full h-16 flex items-center justify-between lg:block lg:w-72 lg:h-dvh p-4 bg-sidebar border-r-2 border-sidebar-border text-sidebar-foreground"
  >
    <div class="flex items-center space-x-2 inline lg:hidden">
      <div
        class="w-8 h-8 flex items-center justify-center bg-sidebar-primary text-sidebar-primary-foreground border-2 border-sidebar-border shadow-brutal-sm"
      >
        <span class="font-bold text-sm">R</span>
      </div>
      <a href="/" class="text-lg font-black">Robynn.ai</a>
    </div>
    <Dialog.Root bind:open>
      <Dialog.Trigger class="lg:hidden"
        ><button
          aria-label="open navigation"
          class="p-2 text-sidebar-foreground hover:text-sidebar-primary"
        >
          <Menu class="h-5 w-5" />
        </button></Dialog.Trigger
      >
      <Dialog.Content
        transition={(node) => fly(node, { x: 300, duration: 300 })}
        class="left-auto right-0 flex h-dvh max-h-screen w-full max-w-sm translate-x-1 flex-col overflow-y-scroll border-y-0 sm:rounded-none bg-sidebar"
      >
        <div class="p-4 border-b-2 border-sidebar-border">
          <div class="flex items-center space-x-2">
            <div
              class="w-8 h-8 flex items-center justify-center bg-sidebar-primary text-sidebar-primary-foreground border-2 border-sidebar-border shadow-brutal-sm"
            >
              <span class="font-bold text-sm">R</span>
            </div>
            <a href="/" class="text-lg font-black text-sidebar-foreground"
              >Robynn.ai</a
            >
          </div>
        </div>
        <ul class="flex flex-col p-4 space-y-1">
          {#each navItems as { href, label, active }}
            <li>
              <a
                {href}
                class="block w-full px-3 py-2 text-sm font-bold transition-colors border-2 {active
                  ? 'bg-sidebar-primary text-sidebar-primary-foreground border-sidebar-border shadow-brutal-sm'
                  : 'text-sidebar-foreground hover:text-sidebar-primary hover:bg-sidebar-accent hover:bg-opacity-20 border-transparent'}"
                onclick={() => (open = false)}
              >
                {label}
              </a>
            </li>
          {/each}
          <div class="flex-grow"></div>
          <li class="pt-4 border-t-2 border-sidebar-border">
            {#if settingsItem}
              <a
                href={settingsItem.href}
                class="block w-full px-3 py-2 text-sm font-bold transition-colors border-2 {settingsItem.active
                  ? 'bg-sidebar-primary text-sidebar-primary-foreground border-sidebar-border shadow-brutal-sm'
                  : 'text-sidebar-foreground hover:text-sidebar-primary hover:bg-sidebar-accent hover:bg-opacity-20 border-transparent'}"
                onclick={() => (open = false)}
              >
                {settingsItem.label}
              </a>
            {/if}
            <a
              href="/sign_out"
              class="block w-full px-3 py-2 text-sm font-bold text-sidebar-foreground hover:text-sidebar-primary hover:bg-sidebar-accent hover:bg-opacity-20 transition-colors border-2 border-transparent"
              onclick={() => (open = false)}
            >
              Sign Out
            </a>
          </li>
        </ul>
      </Dialog.Content>
    </Dialog.Root>
    <ul class="hidden flex-col h-full lg:flex">
      <li class="mb-8">
        <div class="flex items-center space-x-2">
          <div
            class="w-8 h-8 flex items-center justify-center bg-sidebar-primary text-sidebar-primary-foreground border-2 border-sidebar-border shadow-brutal-sm"
          >
            <span class="font-bold text-sm">R</span>
          </div>
          <a href="/" class="text-lg font-black text-sidebar-foreground"
            >Robynn.ai</a
          >
        </div>
      </li>

      <nav class="space-y-1">
        {#each navItems as item}
          <a
            href={item.href}
            class="block px-3 py-2 text-sm font-bold transition-colors border-2 {item.active
              ? 'bg-sidebar-primary text-sidebar-primary-foreground border-sidebar-border shadow-brutal-sm'
              : 'text-sidebar-foreground hover:text-sidebar-primary hover:bg-sidebar-accent hover:bg-opacity-20 border-transparent'}"
          >
            {item.label}
          </a>
        {/each}
      </nav>

      <div class="flex-grow"></div>
      <div class="border-t-2 border-sidebar-border pt-4 space-y-1">
        {#if settingsItem}
          <a
            href={settingsItem.href}
            class="block px-3 py-2 text-sm font-bold transition-colors border-2 {settingsItem.active
              ? 'bg-sidebar-primary text-sidebar-primary-foreground border-sidebar-border shadow-brutal-sm'
              : 'text-sidebar-foreground hover:text-sidebar-primary hover:bg-sidebar-accent hover:bg-opacity-20 border-transparent'}"
          >
            {settingsItem.label}
          </a>
        {/if}
        <a
          href="/sign_out"
          class="block px-3 py-2 text-sm font-bold text-sidebar-foreground hover:text-sidebar-primary hover:bg-sidebar-accent hover:bg-opacity-20 transition-colors border-2 border-transparent"
        >
          Sign Out
        </a>
      </div>
    </ul>
  </nav>

  <div
    class="px-6 lg:px-12 py-6 overflow-y-scroll relative bg-background min-h-full"
  >
    {#if session?.user.is_anonymous}
      <div
        class="text-sm bg-primary text-primary-foreground sticky px-4 py-3 text-center border-2 border-border shadow-brutal mb-6 font-bold"
      >
        You're signed in as an anonymous user. <a
          href="/login/sign_up"
          class="underline font-bold hover:opacity-70"
          >Sign Up to persist your changes</a
        >
      </div>
    {/if}

    {@render children()}
  </div>
</div>
